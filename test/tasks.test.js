const assert = require('assert');
const Tasks = require('../src/tasks.js');

console.log('Starting tasks tests...');

const list = [];
const t1 = Tasks.createTask('Task 1', ['work', 'urgent']);
const t2 = Tasks.createTask('Task 2', ['home']);
Tasks.addTask(list, t1);
Tasks.addTask(list, t2);

const workTasks = Tasks.filterTasksByTag(list, 'work');
assert.strictEqual(workTasks.length, 1);
assert.strictEqual(workTasks[0].description, 'Task 1');

const tags = Tasks.uniqueTags(list).sort();
assert.deepStrictEqual(tags, ['home', 'urgent', 'work']);

// Test priority functionality
const t3 = Tasks.createTask('Task 3', ['work'], '2024-12-31', 'ASAP');
assert.strictEqual(t3.priority, 'ASAP');
assert.strictEqual(t3.deadline, '2024-12-31');

const t4 = Tasks.createTask('Task 4', ['personal'], null, 'Anytime');
assert.strictEqual(t4.priority, 'Anytime');
assert.strictEqual(t4.deadline, null);

// Test that priorities don't appear in uniqueTags
Tasks.addTask(list, t3);
Tasks.addTask(list, t4);
const tagsWithPriority = Tasks.uniqueTags(list).sort();
assert.deepStrictEqual(tagsWithPriority, ['home', 'personal', 'urgent', 'work']);
assert(!tagsWithPriority.includes('ASAP'));
assert(!tagsWithPriority.includes('Anytime'));

// Test Inbox functionality
const today = new Date().toISOString().split('T')[0];
const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split('T')[0];
const tomorrow = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split('T')[0];

const inboxTestTasks = [
  Tasks.createTask('Uncategorized task', []), // Should appear (no tags)
  Tasks.createTask('Tagged task', ['work']), // Should NOT appear (has tags)
  Tasks.createTask('Today event', ['work'], today, 'Evento'), // Should appear (event today)
  Tasks.createTask('Tomorrow event', [], tomorrow, 'Evento'), // Should NOT appear (event tomorrow)
  Tasks.createTask('Today deadline', ['personal'], today, 'Deadline'), // Should appear (deadline ALWAYS)
  Tasks.createTask('Yesterday deadline', [], yesterday, 'Deadline'), // Should appear (deadline ALWAYS)
  Tasks.createTask('Tomorrow deadline', [], tomorrow, 'Deadline'), // Should appear (deadline ALWAYS)
  Tasks.createTask('ASAP task', ['urgent'], null, 'ASAP'), // Should appear (ASAP priority)
  Tasks.createTask('ASAP uncategorized', [], null, 'ASAP'), // Should appear (ASAP priority)
  Tasks.createTask('Anytime task', [], null, 'Anytime'), // Should NOT appear (Anytime priority)
  Tasks.createTask('Anytime with tags', ['work'], null, 'Anytime'), // Should NOT appear (Anytime priority)
];

const inboxTasks = Tasks.getInboxTasks(inboxTestTasks);
assert.strictEqual(inboxTasks.length, 7, 'Should have 7 tasks in inbox');

// Verify specific tasks are included
const inboxDescriptions = inboxTasks.map(t => t.description);
assert(inboxDescriptions.includes('Uncategorized task'));
assert(inboxDescriptions.includes('Today event')); // Event today (even with tags)
assert(inboxDescriptions.includes('Today deadline')); // Deadline ALWAYS
assert(inboxDescriptions.includes('Yesterday deadline')); // Deadline ALWAYS
assert(inboxDescriptions.includes('Tomorrow deadline')); // Deadline ALWAYS
assert(inboxDescriptions.includes('ASAP uncategorized')); // ASAP priority
assert(inboxDescriptions.includes('ASAP task')); // ASAP priority (even with tags)

// Verify specific tasks are NOT included
assert(!inboxDescriptions.includes('Tagged task')); // Has tags but no special priority
assert(!inboxDescriptions.includes('Tomorrow event')); // Event tomorrow (not today)
assert(!inboxDescriptions.includes('Anytime task')); // Anytime priority (never in inbox)
assert(!inboxDescriptions.includes('Anytime with tags')); // Anytime priority (never in inbox)

// Test folder/tag validation and utilities
console.log('Testing folder/tag validation...');

// Test valid tags
const validTag1 = Tasks.validateTag('simple');
assert.strictEqual(validTag1.valid, true);
assert.strictEqual(validTag1.tag, 'simple');

const validTag2 = Tasks.validateTag('Folder/Tag');
assert.strictEqual(validTag2.valid, true);
assert.strictEqual(validTag2.tag, 'Folder/Tag');

const validTag3 = Tasks.validateTag('  Work/Important  ');
assert.strictEqual(validTag3.valid, true);
assert.strictEqual(validTag3.tag, 'Work/Important');

// Test invalid tags
const invalidTag1 = Tasks.validateTag('');
assert.strictEqual(invalidTag1.valid, false);

const invalidTag2 = Tasks.validateTag('Folder/Sub/Tag');
assert.strictEqual(invalidTag2.valid, false);
assert(invalidTag2.error.includes('massimo un livello'));

const invalidTag3 = Tasks.validateTag('/StartWithSlash');
assert.strictEqual(invalidTag3.valid, false);

const invalidTag4 = Tasks.validateTag('EndWithSlash/');
assert.strictEqual(invalidTag4.valid, false);

const invalidTag5 = Tasks.validateTag('Double//Slash');
assert.strictEqual(invalidTag5.valid, false);

// Test folder extraction
const testTags = ['work', 'Home/Chores', 'Home/Bills', 'personal', 'Work/Projects'];
const folders = Tasks.extractFolders(testTags);
assert.deepStrictEqual(folders.sort(), ['Home', 'Work']);

// Test folder utilities
assert.strictEqual(Tasks.getFolderFromTag('Home/Chores'), 'Home');
assert.strictEqual(Tasks.getFolderFromTag('standalone'), null);
assert.strictEqual(Tasks.getTagNameFromTag('Home/Chores'), 'Chores');
assert.strictEqual(Tasks.getTagNameFromTag('standalone'), 'standalone');

const homeTags = Tasks.getTagsInFolder(testTags, 'Home');
assert.deepStrictEqual(homeTags.sort(), ['Home/Bills', 'Home/Chores']);

const standaloneTags = Tasks.getStandaloneTags(testTags);
assert.deepStrictEqual(standaloneTags.sort(), ['personal', 'work']);

console.log('Tasks module tests passed');
