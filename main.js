const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs');
const Database = require('better-sqlite3');

// Setup DB path in userData dir
const dbPath = path.join(app.getPath('userData'), 'tasks.sqlite3');
let db;

function initDatabase() {
  db = new Database(dbPath);
  db.pragma('journal_mode = WAL');
  // Tabella task
  db.prepare(`CREATE TABLE IF NOT EXISTS tasks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    date TEXT,
    type TEXT NOT NULL
  )`).run();
  // Tabella tag
  db.prepare(`CREATE TABLE IF NOT EXISTS tags (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE
  )`).run();
  // Tabella relazione task-tag
  db.prepare(`CREATE TABLE IF NOT EXISTS task_tags (
    task_id INTEGER,
    tag_id INTEGER,
    PRIMARY KEY (task_id, tag_id),
    FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE,
    FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE
  )`).run();
}

function createWindow() {
  const win = new BrowserWindow({
    width: 800,
    height: 600,
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      contextIsolation: true,
      nodeIntegration: false,
    }
  });
  win.loadFile(path.join(__dirname, 'public', 'index.html'));
  win.webContents.openDevTools();
}

app.whenReady().then(() => {
  initDatabase();
  createWindow();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') app.quit();
});

// IPC API
ipcMain.handle('getTasks', () => {
  const tasks = db.prepare('SELECT * FROM tasks').all();
  for (const task of tasks) {
    const tagRows = db.prepare(`SELECT tags.name FROM tags
      JOIN task_tags ON tags.id = task_tags.tag_id WHERE task_tags.task_id = ?`).all(task.id);
    task.tags = tagRows.map(r => r.name);
  }
  return tasks;
});
ipcMain.handle('addTask', (event, task) => {
  const info = db.prepare('INSERT INTO tasks (name, date, type) VALUES (?, ?, ?)').run(task.name, task.date, task.type);
  const taskId = info.lastInsertRowid;
  if (Array.isArray(task.tags)) {
    for (const tagName of task.tags) {
      let tagId;
      try {
        const tagInfo = db.prepare('INSERT INTO tags (name) VALUES (?)').run(tagName);
        tagId = tagInfo.lastInsertRowid;
      } catch {
        tagId = db.prepare('SELECT id FROM tags WHERE name = ?').get(tagName)?.id;
      }
      if (tagId) {
        db.prepare('INSERT OR IGNORE INTO task_tags (task_id, tag_id) VALUES (?, ?)').run(taskId, tagId);
      }
    }
  }
  return taskId;
});
ipcMain.handle('deleteTask', (event, taskId) => {
  db.prepare('DELETE FROM tasks WHERE id = ?').run(taskId);
  db.prepare('DELETE FROM task_tags WHERE task_id = ?').run(taskId);
  return true;
});
ipcMain.handle('updateTask', (event, task) => {
  db.prepare('UPDATE tasks SET name = ?, date = ?, type = ? WHERE id = ?').run(task.name, task.date, task.type, task.id);
  db.prepare('DELETE FROM task_tags WHERE task_id = ?').run(task.id);
  if (Array.isArray(task.tags)) {
    for (const tagName of task.tags) {
      let tagId;
      try {
        const tagInfo = db.prepare('INSERT INTO tags (name) VALUES (?)').run(tagName);
        tagId = tagInfo.lastInsertRowid;
      } catch {
        tagId = db.prepare('SELECT id FROM tags WHERE name = ?').get(tagName)?.id;
      }
      if (tagId) {
        db.prepare('INSERT OR IGNORE INTO task_tags (task_id, tag_id) VALUES (?, ?)').run(task.id, tagId);
      }
    }
  }
  return true;
});
