(function(global, factory){
  if(typeof module === 'object' && module.exports) {
    module.exports = factory();
  } else {
    global.Tasks = factory();
  }
}(this, function(){
  function createTask(description, tags = [], deadline = null, priority = null) {
    return {
      id: Date.now() + Math.random(),
      description,
      tags,
      deadline,
      priority
    };
  }

  function addTask(list, task) {
    list.push(task);
    return list;
  }

  function uniqueTags(list) {
    const set = new Set();
    list.forEach(t => {
      t.tags.forEach(tag => set.add(tag));
    });
    return Array.from(set);
  }

  function filterTasksByTag(list, tag) {
    return list.filter(t => t.tags.includes(tag));
  }

  function getInboxTasks(list) {
    const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format

    return list.filter(task => {
      // 1. Task non categorizzati (senza tags) - solo se non hanno priorità Anytime
      const isUncategorized = task.tags.length === 0 && (
        !task.priority ||
        task.priority === 'ASAP' ||
        (task.priority === 'Evento' && task.deadline === today) ||
        task.priority === 'Deadline'
      );

      // 2. Eventi di oggi (anche se hanno tags)
      const isTodayEvent = task.priority === 'Evento' && task.deadline === today;

      // 3. Deadline SEMPRE (anche se hanno tags) - finché non completata
      const isDeadline = task.priority === 'Deadline';

      // 4. Task ASAP (anche se hanno tags) - sempre urgenti
      const isASAP = task.priority === 'ASAP';

      return isUncategorized || isTodayEvent || isDeadline || isASAP;
    });
  }

  function updateTask(list, updatedTask) {
    return list.map(task => task.id === updatedTask.id ? updatedTask : task);
  }

  function deleteTask(list, taskId) {
    return list.filter(task => task.id !== taskId);
  }

  // Tag validation and folder utilities
  function validateTag(tagString) {
    if (!tagString || typeof tagString !== 'string') {
      return { valid: false, error: 'Tag deve essere una stringa non vuota' };
    }

    const trimmed = tagString.trim();
    if (!trimmed) {
      return { valid: false, error: 'Tag non può essere vuoto' };
    }

    // Count slashes
    const slashCount = (trimmed.match(/\//g) || []).length;
    if (slashCount > 1) {
      return { valid: false, error: 'Tag può avere al massimo un livello di nesting (Folder/Tag)' };
    }

    // Check for invalid patterns
    if (trimmed.startsWith('/') || trimmed.endsWith('/')) {
      return { valid: false, error: 'Tag non può iniziare o finire con "/"' };
    }

    if (trimmed.includes('//')) {
      return { valid: false, error: 'Tag non può contenere "//" consecutivi' };
    }

    return { valid: true, tag: trimmed };
  }

  function extractFolders(tags) {
    const folders = new Set();
    tags.forEach(tag => {
      const folder = getFolderFromTag(tag);
      if (folder) {
        folders.add(folder);
      }
    });
    return Array.from(folders).sort();
  }

  function getFolderFromTag(tag) {
    const slashIndex = tag.indexOf('/');
    return slashIndex !== -1 ? tag.substring(0, slashIndex) : null;
  }

  function getTagNameFromTag(tag) {
    const slashIndex = tag.indexOf('/');
    return slashIndex !== -1 ? tag.substring(slashIndex + 1) : tag;
  }

  function getTagsInFolder(tags, folder) {
    return tags.filter(tag => getFolderFromTag(tag) === folder);
  }

  function getStandaloneTags(tags) {
    return tags.filter(tag => !getFolderFromTag(tag));
  }

  // Fuzzy search functionality
  function fuzzySearchTasks(tasks, query) {
    if (!query || !query.trim()) {
      return tasks;
    }

    const searchTerm = query.toLowerCase().trim();

    return tasks.filter(task => {
      const description = task.description.toLowerCase();

      // Exact match gets highest priority
      if (description.includes(searchTerm)) {
        return true;
      }

      // Fuzzy matching: check if all characters of search term appear in order
      let searchIndex = 0;
      for (let i = 0; i < description.length && searchIndex < searchTerm.length; i++) {
        if (description[i] === searchTerm[searchIndex]) {
          searchIndex++;
        }
      }

      return searchIndex === searchTerm.length;
    }).sort((a, b) => {
      // Sort by relevance: exact matches first, then fuzzy matches
      const aDesc = a.description.toLowerCase();
      const bDesc = b.description.toLowerCase();
      const aExact = aDesc.includes(searchTerm);
      const bExact = bDesc.includes(searchTerm);

      if (aExact && !bExact) return -1;
      if (!aExact && bExact) return 1;

      // If both are exact or both are fuzzy, sort alphabetically
      return aDesc.localeCompare(bDesc);
    });
  }

  return {
    createTask,
    addTask,
    uniqueTags,
    filterTasksByTag,
    getInboxTasks,
    updateTask,
    deleteTask,
    validateTag,
    extractFolders,
    getFolderFromTag,
    getTagNameFromTag,
    getTagsInFolder,
    getStandaloneTags,
    fuzzySearchTasks
  };
}));
