# red-tasks

A minimal task manager built with Electron and React.

React is loaded from a CDN so no build step is required. Tasks can be created in an inbox and tagged dynamically. Selecting a tag from the sidebar filters the tasks. Each task can also store an optional deadline.

## Setup

Install dependencies (requires network access):

```bash
npm install
```

## Development

Run the app in development mode:

```bash
npm run dev
```

## Start

Launch the packaged app:

```bash
npm start
```

## Tests

Run the unit tests:

```bash
npm test
```
